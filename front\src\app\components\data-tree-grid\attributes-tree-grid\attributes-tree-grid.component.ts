import { AttributesService } from 'src/app/services/attributes.service';
import { DataTreeGridComponent } from '../data-tree-grid.component';
import { TreeGridObject } from 'src/app/models/data-tree-grid-type';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  SimpleChange,
  SimpleChanges,
  ViewChildren,
} from '@angular/core';
import {
  RowDataBoundEventArgs,
  RowSelectEventArgs,
} from '@syncfusion/ej2-angular-grids';
import {
  DataStateChangeEventArgs,
  EditService,
  InfiniteScrollService,
  RowExpandedEventArgs,
} from '@syncfusion/ej2-angular-treegrid';
import { Subject } from 'rxjs';
import { MatCheckbox, MatCheckboxChange } from '@angular/material/checkbox';
import { TranslateService } from '@ngx-translate/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { LinkTypesService } from 'src/app/services/structure/link-types.service';
import {
  CTxAttributeSetLevel,
  TxProjectData,
} from 'src/app/models/tx-attribute-set';
import { TxDataType } from 'src/app/models/data';
import { ObjectsTypeService } from 'src/app/services/structure/objects-type.service';

import { ToastComponent } from 'src/app/toast/toast.component';
import { ToastType } from 'src/app/toast/toast.models';
import { ToastService } from 'src/app/toast/toast.service';
import { TxObjectType } from 'src/app/models/tx-object-type';
import { TxAttribute } from 'src/app/models/attribute';
import { ProjectTeexmaUploadParams } from '../../../services/projects.service';
import { FormProjectParameterType } from '../../../models/project-type';
import { BusinessRestService } from '../../../services/texxma-connection/business-rest.service';

@Component({
  selector: 'app-attributes-tree-grid',
  templateUrl: './attributes-tree-grid.component.html',
  styleUrls: ['./attributes-tree-grid.component.scss'],
  providers: [InfiniteScrollService, EditService],
})
export class AttributesTreeGridComponent
  extends DataTreeGridComponent
  implements OnInit, OnChanges, OnDestroy, AfterViewInit
{
  @Input() objectType: TxObjectType;
  @Input() idObjectType: number;
  @Input() showIdColumn = false;
  @Input() showCheckbox = false;
  @Input() displayInheritedAttributes = false;
  public displayCheckedRowsOnInit = false;
  @Input() allowLoadingLinkedAttributes = false;
  @Input() folderCheckable = false;
  @Input() reloadAttributesOnSameOT = false;
  public attributeSetLevels: CTxAttributeSetLevel[] = []; // premit to defined which nodes are checked

  public objectTypesSelectFormGroup: UntypedFormGroup = new UntypedFormGroup({});
  public objectTypesList: TxObjectType[];

  @Output() changeSelection = new EventEmitter<TreeGridObject>();
  @Output() checkChange = new EventEmitter<TxProjectData>();
  @Output() keyUpSearch = new EventEmitter<any>();

  @ViewChildren(MatCheckbox) checkBoxes!: QueryList<MatCheckbox>;

  public loadingIndicator?: any;
  public keyupStream$ = new Subject<void>();
  public isLoading = false;
  public dataFiltered: TreeGridObject[] = undefined; //Initialized to undefined to differentiate initial state from no data state
  public filteredMode = false;
  public noObjectTypeSelected = 'No object type selected';
  public noAttributesForPortal = 'No attributes';
  public noAttributesForListings = 'No attributes';
  public uniqueIdSeparator = '_';
  @Output() onCancel = new EventEmitter<any>();
  @Output() onActive = new EventEmitter<boolean>();
  @Output() objectTypeChange = new EventEmitter<TxObjectType>();
  @Input() tabId: number;
  public active: boolean;
  public rootAttributeTypesDisplayed: TxDataType[] = [
    TxDataType.Boolean,
    TxDataType.ShortText,
    TxDataType.LongText,
    TxDataType.Listing,
    TxDataType.Decimal,
    TxDataType.SingleValue,
    TxDataType.Range,
    TxDataType.RangeMeanValue,
    TxDataType.Date,
    TxDataType.Unknown,
  ];

  constructor(
    public override el: ElementRef,
    public attributeService: AttributesService,
    private otService: ObjectsTypeService,
    private attributesService: AttributesService,
    private linkTypeService: LinkTypesService,
    private toastService: ToastService,
    public changeDetectorRef: ChangeDetectorRef,
    private translate: TranslateService,
    private businessRestService: BusinessRestService,
    private fb: UntypedFormBuilder
  ) {
    super(el);
    this.objectTypesSelectFormGroup = this.fb.group({
      selectedObjectType: [null, Validators.required],
    });
  }

  ngAfterViewInit(): void {}

  override ngOnInit(): void {
    this.otService.listAll().subscribe((r) => {
      this.objectTypesList = r.filter((l) => l.type == 'ottStandard');
    });
    super.ngOnInit();
    this.objectTypesSelectFormGroup.valueChanges.subscribe(() => {
      if (this.objectTypesSelectFormGroup.value?.selectedObjectType?.id) {
        this.isLoading = true;
        this.otService.isReady().subscribe(() => {
          this.objectType = this.otService.find(
            this.objectTypesSelectFormGroup.value?.selectedObjectType?.id
          );
          this.init();
          this.objectTypeChange.emit(this.objectType);
        });
      }
    });
  }
  showGrid() {
    this.isLoading = false
  }
  ngOnChanges(changes: SimpleChanges) {
    const currentOT: SimpleChange = changes['objectType'];
    if (currentOT && currentOT.currentValue) {
      if (this.subscription) {
        this.subscription.unsubscribe();
      }
      if (this.treeGrid) {
        this.treeGrid.closeEdit();
      }
      this.data = [];
      this.init();
    }
    const idObjectType: SimpleChange = changes['idObjectType'];
    if (idObjectType && idObjectType.currentValue) {
      this.objectType = this.otService.find(idObjectType.currentValue);
      if (this.subscription) {
        this.subscription.unsubscribe();
      }
      if (this.treeGrid) {
        this.treeGrid.closeEdit();
      }
      this.init();
    }

    const reloadAttributesOnSameOT: SimpleChange =
      changes['reloadAttributesOnSameOT'];
    if (reloadAttributesOnSameOT && reloadAttributesOnSameOT.currentValue) {
      this.init();
    }
  }

  override ngOnDestroy() {
    super.ngOnDestroy();
    this.keyupStream$.next();
    this.keyupStream$.complete();
  }

  getObjectTypeIconPath(iconID: number): string {
    return this.otService.getIconPath(iconID);
  }

  onNodeExpanding(event: RowExpandedEventArgs): void {
    // this.isLoading = true;
    const currentRowId = (event.data as any).uniqueId;
    const row = this.data.find((d) => d.uniqueId === currentRowId);
    // this.treeGrid.showSpinner();
    let indexFakeChild = this.data.findIndex(
      (d) => d.uniqueId === '-' + currentRowId
    );
    if (row.isParent && indexFakeChild > -1) {
      // this.treeGrid.showSpinner();
      (event as any).cancel = true;
      const attribute = row.txObject as TxAttribute;
      const linkInv = attribute.dataType === TxDataType.LinkInv;
      let idObjectType = linkInv
        ? attribute.linkType.idSourceObjectType
        : attribute.linkType.idDestinationObjectType;

      if (linkInv && attribute.linkType.isAssociative) {
        const associativeLinkType =
          this.linkTypeService.findAssociativeLinkType(
            attribute.linkType.id,
            idObjectType
          );
        if (associativeLinkType) {
          idObjectType = associativeLinkType.idDestinationObjectType;
        }
      }

      this.attributesService
        .listAttributesFromObjectType(idObjectType, this.getDataTypes())
        .subscribe(
          (attributes: TxAttribute[]) => {
            if (attributes.length === 0) {
              row.isParent = false;
            } else {
              // TODO : test this condition
              row.expanded = true;
              indexFakeChild = this.data.findIndex(
                (d) => d.uniqueId === '-' + currentRowId
              );
              this.data.splice(indexFakeChild, 1);
              this.data = this.data.concat(
                this.createRows(attributes, row).reduce(
                  (acc, val) => acc.concat(val),
                  []
                )
              );
              this.dataFiltered = this.data.slice();
              // this.treeGrid.hideSpinner();
              // this.isLoading = false;
            }
          },
          () => {
            // this.isLoading = false;
            // this.treeGrid.hideSpinner();
          }
        );
    }
  }

  isPortalOT(): boolean {
    return this.otService.isPortalOT(this.objectType);
  }

  isEnumerationOT(): boolean {
    return this.otService.isListingOT(this.objectType);
  }

  onInputSearchKeyUp(event: KeyboardEvent) {
    const _selectAttribute = (treeGridObjects: TreeGridObject[]): number => {
      let rowIndex = -1;
      if (treeGridObjects.length) {
        let index = treeGridObjects.findIndex(
          (c) => c.uniqueId === selectedRowId
        );
        if (index > -1) {
          if (index < treeGridObjects.length - 1) {
            index++;
          } else {
            index = 0;
          }
        } else {
          index = 0;
        }

        rowIndex = this.findIndex(treeGridObjects[index].uniqueId);
        this.treeGrid.selectRow(rowIndex);
        setTimeout(() => {
          this.inputSearch.nativeElement.focus();
        }, 200);
      }

      return rowIndex;
    };

    const value = this.inputSearch.nativeElement.value.toLowerCase();
    const selectedRow = this.treeGrid.getSelectedRecords()[0] as any;
    const selectedRowId = selectedRow ? selectedRow.uniqueId : null;
    this.keyUpSearch.emit({ event, value });
    this.searchById = -1;
    if (value) {
      if (event.key === 'Enter') {
        if (!isNaN(Number(value))) {
          const rowsById = this.data.filter((c) => c.id === Number(value));
          const index = _selectAttribute(rowsById);
          if (index > -1) {
            this.searchById = Number(value);
            return;
          }
        }

        const conceptsFound = this.data.filter(
          (c) =>
            c.name.toLowerCase().includes(value) ||
            c.tags.some((t) => t.toLowerCase().includes(value))
        );
        _selectAttribute(conceptsFound);
      } else {
        const conceptMAtched = this.data.find(
          (c) =>
            c.name.toLowerCase().includes(value) ||
            c.tags.some((t) => t.toLowerCase().includes(value))
        );
        if (conceptMAtched) {
          // this.focusItem(conceptMAtched.id, -100);
        }
      }
    }
  }

  findIndex(uniqueId: string) {
    return this.treeGrid.grid.getRowIndexByPrimaryKey(uniqueId);
  }

  //
  // public addFilterEvent() {
  //   this.keyupStream$
  //     .pipe(
  //       map(() =>
  //         this.inputSearch ? this.inputSearch.nativeElement.value : ''
  //       ), // get value
  //       debounceTime(700), // Time in milliseconds between key events
  //       distinctUntilChanged() // If previous query is diffent from current
  //     )
  //     .subscribe((text: string) => {
  //       if (text.length > 2) {
  //         this.dataFiltered = this.data
  //           .filter(
  //             (data) =>
  //               data.name.toLowerCase().includes(text.toLowerCase()) ||
  //               (!isNaN(Number(text))
  //                 ? data.id === Number(text.toLowerCase())
  //                 : false) ||
  //               data.tags.some((tag) =>
  //                 tag.toLowerCase().includes(text.toLowerCase())
  //               )
  //           )
  //           .map((data) => Object.assign({}, data, { idParent: undefined }));
  //       } else if (!isNaN(Number(text))) {
  //         this.dataFiltered = this.data
  //           .filter((data) => data.id === Number(text.toLowerCase()))
  //           .map((data) => Object.assign({}, data, { idParent: undefined }));
  //       } else {
  //         this.dataFiltered = this.data.slice();
  //       }
  //     });
  // }

  removeFilter() {
    this.inputSearch.nativeElement.value = '';
    this.searchById = -1;
  }

  changeAttributeOT(event: RowSelectEventArgs): void {
    const item = event.data as any;
    const attribute = this.data.find(
      (att) => (event.data as TxAttribute).id === att.id
    );
    if (attribute) {
      this.changeSelection.emit(attribute);
    }
  }

  showToast(
    state: string,
    message: string,
    duration: number = 0,
    isPersistent: boolean,
    title?: string
  ): ToastComponent {
    return this.toastService.show({
      templateContext: { test: { state, message, progress: 0 } },
      type: state === 'loading' ? 'information' : (state as ToastType),
      title,
      description: message,
      date: new Date(),
      isPersistent,
      isUnread: true,
      interval: duration,
    });
  }

  override onRowBound(args: RowDataBoundEventArgs) {
    // add custom css rule for child which are not parent
    const data = args.data as any;
    if (!data.isParent) {
      if (data.dataType === TxDataType.Tab) {
        const elements = args.row.getElementsByClassName('e-none');
        if (elements.length) {
          const lastElement = elements[elements.length - 1] as HTMLElement;
          lastElement.setAttribute('style', 'width:' + 17 + 'px');
        }
      } else {
        const widthOfBrothers = 10 * (data.level + 1);
        const i = this.showCheckbox ? 25 : 10;
        const widthTotal = 25 * (data.level + 1) + 2;
        const width = widthTotal - widthOfBrothers;
        const elements = args.row.getElementsByClassName('e-none');
        if (elements.length) {
          const lastElement = elements[elements.length - 1] as HTMLElement;
          lastElement.setAttribute('style', 'width:' + width + 'px');
        }
      }
    }
  }

  onCheck(args: MatCheckboxChange, data: any): void {
      const _checkParent = function (parent: any) {
      if (parent) {
        if (
          ![TxDataType.Tab, TxDataType.Group].includes(parent.txObject.dataType)
        ) {
          parent.checkboxState = 'check';
          parent.isChecked = true;
          this.updateCheckBoxState(parent.uniqueId, true);
        }
        _checkParent(parent.parentItem);
      }
    }.bind(this);

    const _unCheckChildren = function (parent: any) {
      if (parent) {
        if (parent.childRecords) {
          parent.childRecords.forEach((child) => {
            if (
              ![TxDataType.Tab, TxDataType.Group].includes(
                child.txObject.dataType
              )
            ) {
              child.checkboxState = 'uncheck';
              child.isChecked = false;
              this.updateCheckBoxState(child.uniqueId, false);
            }
            _unCheckChildren(child);
          });
        }
      }
    }.bind(this);

    data.checkboxState = args.checked ? 'check' : 'uncheck'; // setting the changed value to checkbox field
    data.isChecked = args.checked;
    const row = this.data.find((d) => d.uniqueId === data.uniqueId);
    row.isChecked = args.checked;
    if (args.checked) {
      // check if the parent is already checked ?
      _checkParent(data.parentItem);
      this.active = true;
    } else {
      _unCheckChildren(data);
      this.active = false;
    }
    this.updateLevels(data, data.uniqueId, args.checked);
    const txProjectData = {
      attributeSetLevels: this.attributeSetLevels,
      objectTypeId: this.objectTypesSelectFormGroup.value.selectedObjectType.id,
      status: this.active,
    };
    this.onActive.emit(this.active);
    this.checkChange.emit(txProjectData);
  }

  uncheckAll() {
    if (this.checkBoxes) {
      this.checkBoxes.forEach((checkbox) => {
        const id = (checkbox as any).id.replace('check', '');
        checkbox.checked = false;

        const row = this.data.find((d) => d.uniqueId === id);
        if (row) {
          row.isChecked = false;
        }
      });
    }
  }

  updateCheckBoxState(rowId: string, checked = true) {
    if (!this.checkBoxes) {
      return;
    }
    const checkbox: MatCheckbox = this.checkBoxes.find(
      (c) => (c as any).id === 'check' + rowId
    );
    if (!checkbox) {
      return;
    }

    checkbox.checked = checked;

    const row = this.data.find((d) => d.uniqueId === rowId);
    if (row) {
      row.isChecked = checked;
    }
  }

  showOnlyChecked() {
    const _fillIds = (levels: CTxAttributeSetLevel[]) => {
      levels.forEach((l) => {
        if (!ids.includes(l.idAttribute)) {
          ids.push(l.idAttribute);
        }

        if (l.childLevels) {
          _fillIds(l.childLevels);
        }
      });
    };

    const _fillData = function (
      levels: CTxAttributeSetLevel[],
      parentId: string
    ) {
      levels.forEach((attributeSetLevel) => {
        const uniqueId: string = parentId
          ? parentId + this.uniqueIdSeparator + attributeSetLevel.idAttribute
          : '' + attributeSetLevel.idAttribute;
        const attribute = attributes.find(
          (a) => a.id === attributeSetLevel.idAttribute
        );
        const row = {
          id: attribute.id,
          uniqueId,
          idParent:
            attribute.idAttributeParent === 0
              ? undefined
              : attribute.idAttributeParent,
          uniqueIdParent: parentId,
          icon: this.attributeService.getIconPathAtt(attribute.id),
          tags: attribute.tags,
          dataType: attribute.dataType,
          isParent: attributeSetLevel.childLevels.length > 0,
          isChecked: true,
          name: attribute.name,
          expanded: true,
          txObject: Object.assign({}, attribute),
        };

        this.data.push(row);

        _fillData(attributeSetLevel.childLevels, uniqueId);
      });
    }.bind(this);

    const ids: number[] = [];
    _fillIds(this.attributeSetLevels);
    // get attributes from attributeSetLevels
    let attributes: TxAttribute[] = [];
    if (!this.filteredMode) {
      this.data2 = JSON.parse(JSON.stringify(this.data));
    }
    this.data = [];
    this.filteredMode = true;
    if (ids.length) {
      this.attributesService.listFromIds(ids).subscribe((atts) => {
        attributes = atts;
        _fillData(this.attributeSetLevels);
        this.dataFiltered = this.data.slice();
      });
    } else {
      this.dataFiltered = this.data.slice();
    }
  }

  showAll() {
    if (!this.data2.length) {
      this.data = [];
      this.data2 = [];
      this.loadRootAttributes();
    } else {
      if (this.filteredMode) {
        this.data = JSON.parse(JSON.stringify(this.data2));
        this.data2 = [];
        if (!this.data.length) {
        }
        this.dataFiltered = this.data.slice();
      }

      this.uncheckAll();
      this.checkFromAttributeSet();
    }
    this.filteredMode = false;
  }

  dataStateChange(state: DataStateChangeEventArgs) {}

  isCheckBoxDisplayed(data: any): boolean {
    if (!data.txObject) {
      return false;
    }

    if (!this.showCheckbox) {
      return false;
    }

    if (
      [TxDataType.Tab, TxDataType.Group].includes(data.txObject.dataType) &&
      !this.folderCheckable
    ) {
      return false;
    }

    return true;
  }

  cancel(event: any) {
    this.onCancel.emit();
  }

  protected checkFromAttributeSet() {
    if (this.attributeSetLevels && this.attributeSetLevels.length) {
      const ids = this.getLevelIdsToCheck();

      ids.forEach((id) => this.updateCheckBoxState(id, true));
    }
  }

  protected getLevelIdsToCheck(): string[] {
    const fillIds = function (
      levels: CTxAttributeSetLevel[],
      idParent: string = ''
    ) {
      levels.forEach((attributeSetLevel) => {
        const uniqueId: string = idParent
          ? idParent + this.uniqueIdSeparator + attributeSetLevel.idAttribute
          : '' + attributeSetLevel.idAttribute;
        ids.push(uniqueId);

        fillIds(attributeSetLevel.childLevels, uniqueId);
      });
    }.bind(this);

    const ids: string[] = [];
    if (!this.attributeSetLevels) {
      return ids;
    }

    fillIds(this.attributeSetLevels);
    return ids;
  }

  protected isLevelChecked(uniqueId: string): boolean {
    const ids = this.getLevelIdsToCheck();
    return ids.includes(uniqueId);
  }

  protected findRootLevel(
    idAttribute: number,
    levels: CTxAttributeSetLevel[] = this.attributeSetLevels
  ): CTxAttributeSetLevel {
    return levels.find((l) => l.idAttribute === idAttribute);
  }

  protected findLevel(levelsIds: number[]): CTxAttributeSetLevel {
    const find = function (levels: CTxAttributeSetLevel[]) {
      const rootLevel = this.findRootLevel(levelsIds[0], levels);
      if (rootLevel) {
        levelsIds.splice(0, 1);
        result = rootLevel;
        if (levelsIds.length) {
          // continue the search
          find(rootLevel.childLevels);
        }
      }
    }.bind(this);

    let result = null;

    find(this.attributeSetLevels);

    return result;
  }

  protected updateLevels(entireData: any, uniqueId: string, checked = true) {
    const createLevels = function (levels: CTxAttributeSetLevel[]) {
      let level = this.findRootLevel(levelIds[0], levels);
      if (!level) {
        level = new CTxAttributeSetLevel({
          idAttribute: levelIds[0],
          dataType: entireData.dataType,
          name: entireData.name,
          childLevels: [],
        });
        levels.push(level);
      }

      levelIds.splice(0, 1);

      if (levelIds.length) {
        createLevels(level.childLevels);
      }
    }.bind(this);

    const levelIds = uniqueId
      .split(this.uniqueIdSeparator)
      .map((id) => parseInt(id, 10));
    const lastLevelId = levelIds[levelIds.length - 1];
    const firstLevelId = levelIds[0];
    const rootLevelToCheck = levelIds.length < 2;

    if (checked) {
      createLevels(this.attributeSetLevels);
    } else {
      if (rootLevelToCheck) {
        this.attributeSetLevels = this.attributeSetLevels.filter(
          (level) => level.idAttribute !== firstLevelId
        );
      } else {
        levelIds.pop();
        const parentLevel = this.findLevel(levelIds);
        if (parentLevel) {
          parentLevel.childLevels = parentLevel.childLevels.filter(
            (level) => level.idAttribute !== lastLevelId
          );
        }
      }
    }
  }

  protected getDataTypes(): TxDataType[] {
    const tabsIncluded = this.rootAttributeTypesDisplayed.includes(
      TxDataType.Tab
    );
    const groupsIncluded = this.rootAttributeTypesDisplayed.includes(
      TxDataType.Group
    );
    let types = [];

    if (this.rootAttributeTypesDisplayed.length) {
      // force the adding of tab and group to load children
      if (!tabsIncluded) {
        types.push(TxDataType.Tab);
      }

      if (!groupsIncluded) {
        types.push(TxDataType.Group);
      }

      types = types.concat(this.rootAttributeTypesDisplayed);
    }
    return types;
  }

  protected filterAttributesFromDataTypes(
    attributes: TxAttribute[]
  ): TxAttribute[] {
    const dataTypes = this.getDataTypes();
    if (dataTypes.length) {
      const targetedAttributes = attributes.filter((a) => {
        if (!this.displayInheritedAttributes && a.isInherited) {
          return false;
        }

        return this.rootAttributeTypesDisplayed.includes(a.dataType);
      });
      let parentIds = [];
      targetedAttributes.forEach((a) => {
        parentIds = parentIds.concat(this.attributesService.getParentsIds(a));
        parentIds.push(a.id);
      });

      if (parentIds.length) {
        attributes = attributes.filter((a) => parentIds.includes(a.id));
      }
    }
    return attributes;
  }

  protected override init() {
    this.data = [];
    this.data2 = [];
    this.attributeSetLevels = [];
    this.filteredMode = false;
    if (
      this.displayCheckedRowsOnInit &&
      this.attributeSetLevels &&
      this.attributeSetLevels.length &&
      this.showCheckbox
    ) {
      this.showOnlyChecked();
    } else {
      this.loadRootAttributes();
    }
  }

  protected loadRootAttributes() {
    // this.isLoading = true;
    if (!this.objectType) {
      return;
    }
    this.subscription = this.attributesService
      .listAttributesFromObjectType(this.objectType.id, this.getDataTypes())
      .subscribe((attributes: TxAttribute[]) => {
        if (this.data.length === 0) {
          this.data = this.createRows(attributes);
          super.init();
          this.dataFiltered = this.data.slice();
        }
        // this.isLoading = false;
      });
  }

  protected createRows(
    attributes: TxAttribute[],
    parentNode?: TreeGridObject
  ): TreeGridObject[] {
    attributes = this.filterAttributesFromDataTypes(attributes);
    const rowsAdding = [];
    const fakeRowsToAdd = [];
    const attributesSorted =
      this.attributesService.listInParentOrder(attributes);
    let rows = attributesSorted.map((att) => {
      const hasDynamicLoading =
        this.allowLoadingLinkedAttributes &&
        this.attributesService.isLinkAttribute(att) &&
        att.dataType !== TxDataType.Listing;
      const id = parentNode
        ? parentNode.uniqueId + this.uniqueIdSeparator + att.id
        : '' + att.id;
      let idParent = parentNode
        ? parentNode.uniqueId
        : att.idAttributeParent
        ? '' + att.idAttributeParent
        : undefined;

      if (parentNode) {
        if (att.idAttributeParent) {
          const child = rowsAdding.find((d) => d.id === att.idAttributeParent);
          idParent = child ? child.uniqueId : parentNode.uniqueId;
        } else {
          idParent = parentNode.uniqueId;
        }
      } else if (att.idAttributeParent) {
        idParent = '' + att.idAttributeParent;
      }

      if (hasDynamicLoading) {
        fakeRowsToAdd.push({
          id: -att.id,
          uniqueId: '-' + id,
          uniqueIdParent: id,
          name: ' ',
          txObject: {},
          tags: [],
          icon: this.attributeService.getIconPath(1),
        });
      }

      const row = {
        id: att.id,
        uniqueId: id,
        idParent:
          att.idAttributeParent === 0 ? undefined : att.idAttributeParent,
        uniqueIdParent: idParent,
        icon: this.attributeService.getIconPathAtt(att.id),
        tags: att.tags,
        dataType: att.dataType,
        isParent: hasDynamicLoading,
        isChecked: this.isLevelChecked(id),
        name: att.name,
        expanded: hasDynamicLoading
          ? false
          : this.getExpandedState(att, this.data),
        txObject: Object.assign({}, att),
      };
      rowsAdding.push(row);
      return row;
    });

    rows = rows.concat(fakeRowsToAdd);

    rows.sort((a, b) => a.txObject.order - b.txObject.order);
    return rows;
  }

  protected override getExpandedState(
    obj: any,
    objGrid: TreeGridObject[]
  ): boolean {
    if (objGrid) {
      const o = objGrid.find((og) => og.uniqueId === obj.uniqueId);
      return o ? o.expanded : true;
    }
    return true;
  }
}
