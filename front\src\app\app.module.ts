import { SessionService } from './services/session/session.service';
/** Angular modules */
import {
  CommonModule,
  DatePipe,
  TitleCasePipe,
  UpperCasePipe,
} from '@angular/common';
import {
  HttpClient,
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import { JwtInterceptor } from './interceptors/JWT.Interceptor';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { AppRoutingModule } from './app-routing.module';

import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';

/**Component.ts*/
import { AppComponent } from './app.component';
import { DialogSaveFunctionComponent } from './components/dialog-save-function/dialog-save-function.component';
import { MainChartComponent } from './main-container-group/main-chart/main-chart.component';
import { TableComponent } from './main-container-group/table/table.component';
import { MainNavComponent } from './main-nav/main-nav.component'; //import depuis tx admin
import { ToastComponent } from './toast/toast.component';
import { RightPaneComponent } from './main-container-group/sidebars/right-pane/right-pane.component';
import { SidebarTemplateComponent } from './main-container-group/sidebars/sidebar-template/sidebar-template.component';
import { SmallRightPaneComponent } from './main-container-group/sidebars/small-right-pane/small-right-pane.component';
import { ChartTableComponent } from './main-container-group/main-chart/chart-table/chart-table.component';
import { ProjectSelectionComponent } from './homepage/project-selection/project-selection.component';
import { DialogNewProjectComponent } from './components/dialog-new-project/dialog-new-project.component';
import { DialogDeleteComponent } from './components/dialog-delete/dialog-delete.component';
import { FunctionsComponent } from './main-container-group/pane-content/functions/functions/functions.component';
import { ManageFunctionsComponent } from './main-container-group/pane-content/functions/manage-functions/manage-functions.component';
import { AlgorithmsComponent } from './main-container-group/pane-content/algorithms/algorithms.component';
import { TeexmaNewProjectComponent } from './components/dialog-new-project/teexma-new-project/teexma-new-project.component';
import { FileNewProjectComponent } from './components/dialog-new-project/file-new-project/file-new-project.component';
import { PlotSettingsComponent } from './main-container-group/pane-content/plot-settings/plot-settings.component';
import { MeasuresComponent } from './main-container-group/pane-content/measures/measures.component';
import { FiltersComponent } from './main-container-group/pane-content/filters/filters.component';
import { DistributionPlotSettingsComponent } from './main-container-group/pane-content/distribution-plot-settings/distribution-plot-settings.component';
import { CorrelationAndRepartitionChartComponent } from './main-container-group/statistics/correlation-and-repartition-chart/correlation-and-repartition-chart.component';
/**Directives*/

/** Services */
import { ConfigService } from './services/config/config.service';
import { AlgorithmsService } from './services/algorithms.service';
import { AttributesService } from './services/attributes.service';
import { AuthenticationService } from './services/authentication/authentication.service';
import { FiltersService } from './services/filters.service';
import { FunctionsService } from './services/functions.service';
import { MainNavService } from './services/main-nav.service';
import { ObjectsService } from './services/objects.service';
import { TableService } from './services/table.service';
import { MeasuresFacadeService } from './services/main-container/measures-facade.service';
import { FiltersFacadeService } from './services/main-container/filters-facade.service';
import { ChartTableFacadeService } from './services/main-container/chart-table-facade.service';

/**Sub Services that use other service  */
import { FunctionsSubService } from './main-container-group/pane-content/functions/functions-sub.service';

/** Fontawesome modules */
import {
  FaIconLibrary,
  FontAwesomeModule,
} from '@fortawesome/angular-fontawesome';
import { fal } from '@fortawesome/pro-light-svg-icons';
import { fas } from '@fortawesome/pro-solid-svg-icons';

/** Imported angular material modules */
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule } from '@angular/material/sort';
import { MatStepperModule } from '@angular/material/stepper';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
/** Angular material CDK modules */
import { ClipboardModule } from '@angular/cdk/clipboard';
import { LayoutModule } from '@angular/cdk/layout';

/**  Imported syncfusion modules */
import {
  ButtonModule,
  CheckBoxModule,
  SwitchModule,
} from '@syncfusion/ej2-angular-buttons';
import {
  CalendarModule,
  DatePickerAllModule,
  DatePickerModule,
} from '@syncfusion/ej2-angular-calendars';
import { DropDownListAllModule } from '@syncfusion/ej2-angular-dropdowns';
import {
  EditService,
  FilterService,
  GridAllModule,
  GroupService,
  PagerModule,
  PageService,
  ResizeService,
  RowDDService,
  SelectionService,
  SortService,
  ToolbarService,
} from '@syncfusion/ej2-angular-grids';
import { TooltipModule } from '@syncfusion/ej2-angular-popups';

import {
  NumericTextBoxAllModule,
  TextBoxModule,
  UploaderModule,
} from '@syncfusion/ej2-angular-inputs';
import {
  ContextMenuModule,
  SidebarAllModule,
  SidebarModule,
  ToolbarModule,
  TreeViewModule,
} from '@syncfusion/ej2-angular-navigations';
import { DialogModule } from '@syncfusion/ej2-angular-popups';
import { TreeGridModule } from '@syncfusion/ej2-angular-treegrid';

/* Import ngx-translate and the http loader */
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { RemoveUnderscorePipe } from './pipes/removeUnderscore.pipe';
import { EscapeHtmlPipe } from './pipes/escape-html.pipe';
import { RemoveWordPipe } from './pipes/removeWord.pipe';
import { AppService } from './services/app.service';
import { TranslationParser } from './translation/translation-parser';
import { HighlightSearchPipe } from './pipes/highlight-search.pipe';
import { DataTypePipe } from './pipes/data-type.pipe';
import { NoRecordComponent } from './components/no-record/no-record.component';
import { NewProjectParameterFormComponent } from './components/dialog-new-project/new-project-parameter-form/new-project-parameter-form.component';
import { AlgoAppliedComponent } from './main-container-group/algo-applied/algo-applied.component';
import { AlgorithmsDetailsComponent } from './main-container-group/pane-content/algorithms-details/algorithms-details.component';
import { DistributionChartComponent } from './main-container-group/statistics/distribution-chart/distribution-chart.component';
import { MatCardModule } from '@angular/material/card';
import { AlgorithmDeletionComponent } from './components/algorithm-deletion/algorithm-deletion.component';
import { DistributionChartTableComponent } from './main-container-group/statistics/distribution-chart/distribution-chart-table/distribution-chart-table.component';
import { MatNativeDateModule } from '@angular/material/core';
import { HelpBoxComponent } from './help-box/help-box.component';
import { MoveableHelpBoxComponent } from './help-box/moveable-help-box/moveable-help-box.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { CorrelationAndRepartitionPlotSettingsComponent } from './main-container-group/pane-content/correlation-and-repartition-plot-settings/correlation-and-repartition-plot-settings.component';
import { CorrelationAndRepartitionTableComponent } from './main-container-group/statistics/correlation-and-repartition-chart/correlation-and-repartition-table/correlation-and-repartition-table.component';
import { AlertMessageComponent } from './components/alert-message/alert-message.component';
import { AlertMessageService } from './services/alert-message.service';
import { ErrorService } from './services/errors/errors.service';
import { HttpErrorInterceptor } from './interceptors/http-error.interceptor';
import { DialogMessageComponent } from './components/dialog-message/dialog-message.component';
import { MergeObjectsPipe } from './pipes/merge-objets.pipe';
import {
  AbstractSessionService,
  TX_ENVIRONMENT_TOKEN,
  AttributeTreeGridComponent,
  TxConfigService,
  TxLibService,
  TxObjectsTypeDropdownComponent,
  TxTreeGridComponent,
  TxGridColumnTemplate,
  TxGridComponent,
} from '@bassetti-group/tx-web-core';
import { InterpolationsComponent } from './main-container-group/pane-content/interpolations/interpolations.component';
import { MathToLatexPipe } from './pipes/math-to-latex.pipe';
import { RenderMathComponent } from './components/render-math/render-math.component';
import { environment } from 'src/environments/environment';
import { DiagnosticComponent } from './components/diagnostic/diagnostic.component';
import { DialogDuplicateProjectComponent } from './components/dialog-duplicate-project/dialog-duplicate-project.component';
import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { SciNotationPipe } from './pipes/sci-notation.pipe';
import { MaximizeDialogToggle } from './directives/maximize-dialog-toggle.directive';
import { TrendCurvesComponent } from './main-container-group/pane-content/trend-curves/trend-curves.component';

// Add dependencies to FusionChartsModule

@NgModule({
  declarations: [
    AppComponent,
    MainChartComponent,
    MainNavComponent,
    ToastComponent,
    TableComponent,
    DistributionChartTableComponent,
    AlgoAppliedComponent,
    AlgorithmsDetailsComponent,
    AlgorithmDeletionComponent,
    RemoveUnderscorePipe,
    HighlightSearchPipe,
    EscapeHtmlPipe,
    RemoveWordPipe,
    MergeObjectsPipe,
    RightPaneComponent,
    SmallRightPaneComponent,
    SidebarTemplateComponent,
    DialogSaveFunctionComponent,
    ChartTableComponent,
    ProjectSelectionComponent,
    DialogNewProjectComponent,
    DialogDeleteComponent,
    FunctionsComponent,
    ManageFunctionsComponent,
    AlgorithmsComponent,
    DataTypePipe,
    NoRecordComponent,
    TeexmaNewProjectComponent,
    NewProjectParameterFormComponent,
    FileNewProjectComponent,
    FiltersComponent,
    PlotSettingsComponent,
    MeasuresComponent,
    AlgoAppliedComponent,
    AlgorithmsDetailsComponent,
    DistributionChartComponent,
    DistributionPlotSettingsComponent,
    CorrelationAndRepartitionChartComponent,
    AlgorithmDeletionComponent,
    HelpBoxComponent,
    MoveableHelpBoxComponent,
    CorrelationAndRepartitionPlotSettingsComponent,
    CorrelationAndRepartitionTableComponent,
    AlertMessageComponent,
    DialogMessageComponent,
    InterpolationsComponent,
    MathToLatexPipe,
    RenderMathComponent,
    DiagnosticComponent,
    DialogDuplicateProjectComponent,
    SciNotationPipe,
    MaximizeDialogToggle,
    TrendCurvesComponent,
  ],
  bootstrap: [AppComponent],
  exports: [DataTypePipe],
  imports: [
    /** Angular modules */
    BrowserModule,
    ReactiveFormsModule,
    AppRoutingModule,
    FormsModule,
    BrowserAnimationsModule,
    CommonModule,
    NgMultiSelectDropDownModule,
    RouterModule.forRoot([]),
    /**FontAwesome Module */
    FontAwesomeModule,
    /* Imported angular material modules */
    MatIconModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatTooltipModule,
    MatMenuModule,
    MatGridListModule,
    MatListModule,
    MatRadioModule,
    MatButtonToggleModule,
    MatSidenavModule,
    MatToolbarModule,
    MatTabsModule,
    MatBottomSheetModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatFormFieldModule,
    MatStepperModule,
    MatCheckboxModule,
    MatTableModule,
    MatSortModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatCardModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatExpansionModule,
    /** Angular material CDK modules */
    ClipboardModule,
    LayoutModule,
    DragDropModule,
    NumericTextBoxAllModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: translateLoader,
        deps: [HttpClient, ErrorService],
      },
    }),
    /** Imported syncfusion modules */
    SidebarAllModule,
    GridAllModule,
    PagerModule,
    SidebarModule,
    TreeViewModule,
    ContextMenuModule,
    TextBoxModule,
    UploaderModule,
    ButtonModule,
    SwitchModule,
    DialogModule,
    CalendarModule,
    DatePickerModule,
    TreeGridModule,
    ToolbarModule,
    DatePickerAllModule,
    DropDownListAllModule,
    ReactiveFormsModule,
    FormsModule,
    CheckBoxModule,
    TooltipModule,
    AttributeTreeGridComponent,
    TxGridComponent,
    TxObjectsTypeDropdownComponent,
    TxObjectsTypeDropdownComponent,
    TxTreeGridComponent,
    TxGridColumnTemplate,
  ],
  providers: [
    { provide: AbstractSessionService, useExisting: SessionService },
    { provide: TX_ENVIRONMENT_TOKEN, useValue: environment },
    { provide: TxConfigService, useExisting: ConfigService },
    {
      provide: APP_INITIALIZER,
      useFactory: onInitApp,
      deps: [
        ConfigService,
        SessionService,
        AuthenticationService,
        TxLibService,
      ],
      multi: true,
    },
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: HttpErrorInterceptor, multi: true },
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false },
    },
    FilterService,
    PageService,
    ResizeService,
    SelectionService,
    RowDDService,
    AppService,
    ErrorService,
    SortService,
    ToolbarService,
    EditService,
    ConfigService,
    DatePipe,
    UpperCasePipe,
    TitleCasePipe,
    ObjectsService,
    GroupService,
    AlgorithmsService,
    AttributesService,
    AuthenticationService,
    FiltersService,
    FunctionsService,
    MainNavService,
    TableService,
    FunctionsSubService,
    MeasuresFacadeService,
    FiltersFacadeService,
    ChartTableFacadeService,
    AlertMessageService,
    provideHttpClient(withInterceptorsFromDi()),
  ],
})
export class AppModule {
  constructor(library: FaIconLibrary) {
    library.addIconPacks(fal, fas); //Strong Font Awesome Icons, "fas", import didn't work
  }
}

/**
 * TxAdmin has a translator: a set of sentences with correspondence in multiple languages,
 * This service is not used. However, the Neo Teexma navigation modules use the Session service which depends on the translation parser
 */
export function translateLoader(http: HttpClient, errorService: ErrorService) {
  const files = ['./assets/i18n/', './assets/tx-web-core/i18n/'];

  return new TranslationParser(http, errorService, files, '.json');
}

/**
 *  Initialization automatically launches config-service.ts
 */
export function onInitApp(
  config: ConfigService,
  sessionService: SessionService,
  authService: AuthenticationService
) {
  return (): Promise<any> => {
    return config.load().then(() => {
      sessionService.init();
      if (authService.isTokenActive()) {
        sessionService.load();
      }
    });
  };
}
